import numpy as np
import pandas as pd
from tensorflow.keras.models import load_model
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
import matplotlib.pyplot as plt
import seaborn as sns

# ==== CONFIG ====
MODEL_PATH = "lstm_candle_predictor.h5"
X_test_file = "X_test.npy"
y_test_file = "y_test.npy"

def load_test_data():
    """Load test data"""
    print("📊 Loading test data...")
    X_test = np.load(X_test_file)
    y_test = np.load(y_test_file)
    print(f"Test samples: {X_test.shape[0]}")
    print(f"Input shape: {X_test.shape[1:]}")
    return X_test, y_test

def evaluate_comprehensive(model, X_test, y_test):
    """Comprehensive model evaluation"""
    print("\n🔍 Performing comprehensive evaluation...")
    
    # Make predictions
    y_pred_prob = model.predict(X_test, verbose=1)
    y_pred = (y_pred_prob > 0.5).astype(int).flatten()
    y_pred_prob = y_pred_prob.flatten()
    
    # Basic metrics
    test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)
    
    print(f"\n📈 PERFORMANCE METRICS")
    print("="*50)
    print(f"Test Loss: {test_loss:.4f}")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    
    # ROC AUC Score
    try:
        auc_score = roc_auc_score(y_test, y_pred_prob)
        print(f"ROC AUC Score: {auc_score:.4f}")
    except Exception as e:
        print(f"Could not calculate AUC: {e}")
        auc_score = None
    
    # Classification report
    print(f"\n📊 CLASSIFICATION REPORT")
    print("="*50)
    print(classification_report(y_test, y_pred, target_names=['Down (0)', 'Up (1)']))
    
    # Confusion matrix
    print(f"\n🔍 CONFUSION MATRIX")
    print("="*50)
    cm = confusion_matrix(y_test, y_pred)
    print("Actual vs Predicted:")
    print(f"         Down  Up")
    print(f"Down     {cm[0,0]:4d}  {cm[0,1]:4d}")
    print(f"Up       {cm[1,0]:4d}  {cm[1,1]:4d}")
    
    # Calculate additional metrics
    tn, fp, fn, tp = cm.ravel()
    precision_up = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall_up = tp / (tp + fn) if (tp + fn) > 0 else 0
    precision_down = tn / (tn + fn) if (tn + fn) > 0 else 0
    recall_down = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    print(f"\n📊 DETAILED METRICS")
    print("="*50)
    print(f"True Positives (UP correctly predicted): {tp}")
    print(f"True Negatives (DOWN correctly predicted): {tn}")
    print(f"False Positives (UP incorrectly predicted): {fp}")
    print(f"False Negatives (DOWN incorrectly predicted): {fn}")
    print(f"UP Precision: {precision_up:.4f}")
    print(f"UP Recall: {recall_up:.4f}")
    print(f"DOWN Precision: {precision_down:.4f}")
    print(f"DOWN Recall: {recall_down:.4f}")
    
    return {
        'accuracy': test_accuracy,
        'loss': test_loss,
        'auc': auc_score,
        'predictions': y_pred,
        'probabilities': y_pred_prob,
        'confusion_matrix': cm
    }

def plot_evaluation_charts(y_test, y_pred_prob, y_pred, cm, auc_score=None):
    """Create evaluation visualizations"""
    print("\n📊 Creating evaluation charts...")
    
    plt.figure(figsize=(15, 10))
    
    # 1. Confusion Matrix Heatmap
    plt.subplot(2, 3, 1)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Down', 'Up'], yticklabels=['Down', 'Up'])
    plt.title('Confusion Matrix')
    plt.ylabel('Actual')
    plt.xlabel('Predicted')
    
    # 2. Prediction Probability Distribution
    plt.subplot(2, 3, 2)
    plt.hist(y_pred_prob[y_test == 0], bins=30, alpha=0.7, label='Actual Down', color='red')
    plt.hist(y_pred_prob[y_test == 1], bins=30, alpha=0.7, label='Actual Up', color='green')
    plt.xlabel('Prediction Probability')
    plt.ylabel('Frequency')
    plt.title('Prediction Probability Distribution')
    plt.legend()
    
    # 3. ROC Curve
    if auc_score is not None:
        plt.subplot(2, 3, 3)
        try:
            fpr, tpr, _ = roc_curve(y_test, y_pred_prob)
            plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {auc_score:.2f})')
            plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
            plt.xlim([0.0, 1.0])
            plt.ylim([0.0, 1.05])
            plt.xlabel('False Positive Rate')
            plt.ylabel('True Positive Rate')
            plt.title('ROC Curve')
            plt.legend(loc="lower right")
        except Exception as e:
            plt.text(0.5, 0.5, f'ROC curve error: {e}', ha='center', va='center')
    
    # 4. Prediction Confidence Distribution
    plt.subplot(2, 3, 4)
    confidence = np.maximum(y_pred_prob, 1 - y_pred_prob)
    plt.hist(confidence, bins=30, alpha=0.7, color='purple')
    plt.xlabel('Prediction Confidence')
    plt.ylabel('Frequency')
    plt.title('Prediction Confidence Distribution')
    
    # 5. Accuracy by Confidence Level
    plt.subplot(2, 3, 5)
    confidence_bins = np.linspace(0.5, 1.0, 11)
    accuracies = []
    bin_centers = []
    
    for i in range(len(confidence_bins) - 1):
        mask = (confidence >= confidence_bins[i]) & (confidence < confidence_bins[i + 1])
        if np.sum(mask) > 0:
            acc = np.mean(y_test[mask] == y_pred[mask])
            accuracies.append(acc)
            bin_centers.append((confidence_bins[i] + confidence_bins[i + 1]) / 2)
    
    if accuracies:
        plt.plot(bin_centers, accuracies, 'o-', color='blue')
        plt.xlabel('Confidence Level')
        plt.ylabel('Accuracy')
        plt.title('Accuracy vs Confidence Level')
        plt.grid(True, alpha=0.3)
    
    # 6. Class Distribution
    plt.subplot(2, 3, 6)
    actual_counts = np.bincount(y_test)
    predicted_counts = np.bincount(y_pred)
    
    x = np.arange(2)
    width = 0.35
    
    plt.bar(x - width/2, actual_counts, width, label='Actual', alpha=0.7)
    plt.bar(x + width/2, predicted_counts, width, label='Predicted', alpha=0.7)
    plt.xlabel('Class')
    plt.ylabel('Count')
    plt.title('Class Distribution')
    plt.xticks(x, ['Down', 'Up'])
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('model_evaluation.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("📊 Evaluation charts saved as 'model_evaluation.png'")

def analyze_prediction_patterns(y_test, y_pred_prob, y_pred):
    """Analyze prediction patterns"""
    print(f"\n🔍 PREDICTION PATTERN ANALYSIS")
    print("="*50)
    
    # High confidence predictions
    high_conf_threshold = 0.8
    low_conf_threshold = 0.6
    
    high_conf_mask = (y_pred_prob > high_conf_threshold) | (y_pred_prob < (1 - high_conf_threshold))
    low_conf_mask = (y_pred_prob >= (1 - low_conf_threshold)) & (y_pred_prob <= low_conf_threshold)
    
    if np.sum(high_conf_mask) > 0:
        high_conf_accuracy = np.mean(y_test[high_conf_mask] == y_pred[high_conf_mask])
        print(f"High confidence predictions (>{high_conf_threshold:.1f}): {np.sum(high_conf_mask)} samples")
        print(f"High confidence accuracy: {high_conf_accuracy:.4f}")
    
    if np.sum(low_conf_mask) > 0:
        low_conf_accuracy = np.mean(y_test[low_conf_mask] == y_pred[low_conf_mask])
        print(f"Low confidence predictions (<{low_conf_threshold:.1f}): {np.sum(low_conf_mask)} samples")
        print(f"Low confidence accuracy: {low_conf_accuracy:.4f}")
    
    # Prediction distribution
    up_predictions = np.sum(y_pred == 1)
    down_predictions = np.sum(y_pred == 0)
    print(f"UP predictions: {up_predictions} ({up_predictions/len(y_pred):.2%})")
    print(f"DOWN predictions: {down_predictions} ({down_predictions/len(y_pred):.2%})")

def main():
    """Main evaluation function"""
    print("🎯 MODEL EVALUATION SCRIPT")
    print("="*50)
    
    # Load test data
    X_test, y_test = load_test_data()
    
    # Load model
    print("🧠 Loading trained model...")
    try:
        model = load_model(MODEL_PATH)
        print(f"✅ Model loaded successfully from {MODEL_PATH}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Perform evaluation
    results = evaluate_comprehensive(model, X_test, y_test)
    
    # Create visualizations
    plot_evaluation_charts(
        y_test, 
        results['probabilities'], 
        results['predictions'], 
        results['confusion_matrix'],
        results['auc']
    )
    
    # Analyze patterns
    analyze_prediction_patterns(y_test, results['probabilities'], results['predictions'])
    
    print(f"\n🎉 Evaluation completed!")
    print(f"📊 Final Test Accuracy: {results['accuracy']:.4f}")
    if results['auc']:
        print(f"📊 ROC AUC Score: {results['auc']:.4f}")

if __name__ == "__main__":
    main()
