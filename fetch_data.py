import requests
import pandas as pd
import time
import datetime
import numpy as np

# ==== CONFIG ====
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"  # Replace with your actual API key
ACCOUNT_ID = "101-004-********-001"
INSTRUMENT = "EUR_USD"  # Currency pair to fetch
GRANULARITY = "M1"  # 1-minute candles
BATCH_SIZE = 500  # OANDA maximum per request
TOTAL_CANDLES = 150_000  # Total candles to fetch
OUTPUT_FILE_RAW = "historical_data_raw.csv"
OUTPUT_FILE_NORM = "historical_data_normalized.csv"

# ==== API HEADERS ====
headers = {
    "Authorization": f"Bearer {ACCESS_TOKEN}"
}

# ==== FETCH FUNCTION ====
def fetch_candles(start_from=None, count=500):
    url = f"https://api-fxpractice.oanda.com/v3/instruments/{INSTRUMENT}/candles"
    params = {
        "granularity": GRANULARITY,
        "count": count,
        "price": "M",
        "smooth": False
    }
    if start_from:
        params["from"] = start_from
        params["includeFirst"] = True

    response = requests.get(url, headers=headers, params=params)

    if response.status_code != 200:
        print("❌ Error:", response.status_code)
        print("Message:", response.text)
        return {"candles": []}

    return response.json()

# ==== TECHNICAL INDICATORS ====
def calculate_rsi(prices, period=14):
    """Calculate Relative Strength Index"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD (Moving Average Convergence Divergence)"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    macd_histogram = macd - macd_signal
    return macd, macd_signal, macd_histogram

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, lower_band, sma

def calculate_ema(prices, period=20):
    """Calculate Exponential Moving Average"""
    return prices.ewm(span=period).mean()

def add_technical_indicators(df):
    """Add all technical indicators to the dataframe"""
    df = df.copy()

    # RSI
    df['rsi'] = calculate_rsi(df['close'])

    # MACD
    macd, macd_signal, macd_hist = calculate_macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_histogram'] = macd_hist

    # Bollinger Bands
    bb_upper, bb_lower, bb_sma = calculate_bollinger_bands(df['close'])
    df['bb_upper'] = bb_upper
    df['bb_lower'] = bb_lower
    df['bb_sma'] = bb_sma
    df['bb_width'] = bb_upper - bb_lower
    df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)

    # EMAs
    df['ema_20'] = calculate_ema(df['close'], 20)
    df['ema_50'] = calculate_ema(df['close'], 50)

    # Price momentum features
    df['price_change'] = df['close'].pct_change()
    df['high_low_ratio'] = df['high'] / df['low']
    df['close_open_ratio'] = df['close'] / df['open']

    # Volume features
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']

    return df

# ==== PARSE FUNCTION ====
def parse_candles(candles):
    rows = []
    for candle in candles:
        if candle.get("complete"):
            mid = candle["mid"]
            open_p = float(mid["o"])
            close_p = float(mid["c"])
            direction = "bullish" if close_p > open_p else ("bearish" if close_p < open_p else "neutral")
            rows.append({
                "time": candle["time"],
                "open": open_p,
                "high": float(mid["h"]),
                "low": float(mid["l"]),
                "close": close_p,
                "volume": candle["volume"],
                "direction": direction
            })
    return rows

# ==== MAIN LOOP ====
print(f"📥 Starting candle fetch...")
all_data = []

# Start from 180 days ago
start_time = (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=180)).isoformat().replace('+00:00', 'Z')
last_time = start_time

batches_needed = TOTAL_CANDLES // BATCH_SIZE
if TOTAL_CANDLES % BATCH_SIZE != 0:
    batches_needed += 1

for i in range(batches_needed):
    print(f"🔄 Fetching batch {i + 1} ({BATCH_SIZE} candles)... From: {last_time}")
    candles_json = fetch_candles(start_from=last_time, count=BATCH_SIZE)

    if "candles" not in candles_json or not candles_json["candles"]:
        print("⚠️ No candles returned. Ending fetch.")
        break

    batch = parse_candles(candles_json["candles"])

    if not batch:
        print("⚠️ No complete candles in this batch. Ending fetch.")
        break

    # Remove the first candle of next batch to avoid overlap
    if i > 0:
        batch = batch[1:]

    all_data.extend(batch)

    last_time = candles_json["candles"][-1]["time"]

    if len(all_data) >= TOTAL_CANDLES:
        print(f"✅ Reached {TOTAL_CANDLES} candles.")
        all_data = all_data[:TOTAL_CANDLES]
        break

    time.sleep(1)  # Respect OANDA rate limit

# ==== SAVE RAW DATA TO CSV ====
if all_data:
    df = pd.DataFrame(all_data)
    df.to_csv(OUTPUT_FILE_RAW, index=False)
    print(f"✅ Saved raw data ({len(df)}) candles to {OUTPUT_FILE_RAW}")

    # ==== ADD TECHNICAL INDICATORS ====
    print("📊 Adding technical indicators...")
    df_with_indicators = add_technical_indicators(df)

    # Remove rows with NaN values (from technical indicators)
    df_clean = df_with_indicators.dropna()
    print(f"📊 After adding indicators and cleaning: {len(df_clean)} candles")

    # Save data with technical indicators (no normalization here)
    df_clean.to_csv(OUTPUT_FILE_NORM, index=False)
    print(f"✅ Saved data with technical indicators to {OUTPUT_FILE_NORM}")

    # Display feature summary
    feature_cols = [col for col in df_clean.columns if col not in ['time', 'direction']]
    print(f"📈 Features available: {len(feature_cols)}")
    print(f"Features: {', '.join(feature_cols)}")

else:
    print("❌ No data fetched.")
