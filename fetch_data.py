import requests
import pandas as pd
import time
import datetime
from sklearn.preprocessing import MinMaxScaler

# ==== CONFIG ====
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"  # Replace with your actual API key
ACCOUNT_ID = "101-004-********-001"
INSTRUMENT = "EUR_USD"  # Currency pair to fetch
GRANULARITY = "M1"  # 1-minute candles
BATCH_SIZE = 500  # OANDA maximum per request
TOTAL_CANDLES = 150_000  # Total candles to fetch
OUTPUT_FILE_RAW = "historical_data_raw.csv"
OUTPUT_FILE_NORM = "historical_data_normalized.csv"

# ==== API HEADERS ====
headers = {
    "Authorization": f"Bearer {ACCESS_TOKEN}"
}

# ==== FETCH FUNCTION ====
def fetch_candles(start_from=None, count=500):
    url = f"https://api-fxpractice.oanda.com/v3/instruments/{INSTRUMENT}/candles"
    params = {
        "granularity": GRANULARITY,
        "count": count,
        "price": "M",
        "smooth": False
    }
    if start_from:
        params["from"] = start_from
        params["includeFirst"] = True

    response = requests.get(url, headers=headers, params=params)

    if response.status_code != 200:
        print("❌ Error:", response.status_code)
        print("Message:", response.text)
        return {"candles": []}

    return response.json()

# ==== PARSE FUNCTION ====
def parse_candles(candles):
    rows = []
    for candle in candles:
        if candle.get("complete"):
            mid = candle["mid"]
            open_p = float(mid["o"])
            close_p = float(mid["c"])
            direction = "bullish" if close_p > open_p else ("bearish" if close_p < open_p else "neutral")
            rows.append({
                "time": candle["time"],
                "open": open_p,
                "high": float(mid["h"]),
                "low": float(mid["l"]),
                "close": close_p,
                "volume": candle["volume"],
                "direction": direction
            })
    return rows

# ==== MAIN LOOP ====
print(f"📥 Starting candle fetch...")
all_data = []

# Start from 180 days ago
start_time = (datetime.datetime.utcnow() - datetime.timedelta(days=180)).isoformat() + "Z"
last_time = start_time

batches_needed = TOTAL_CANDLES // BATCH_SIZE
if TOTAL_CANDLES % BATCH_SIZE != 0:
    batches_needed += 1

for i in range(batches_needed):
    print(f"🔄 Fetching batch {i + 1} ({BATCH_SIZE} candles)... From: {last_time}")
    candles_json = fetch_candles(start_from=last_time, count=BATCH_SIZE)

    if "candles" not in candles_json or not candles_json["candles"]:
        print("⚠️ No candles returned. Ending fetch.")
        break

    batch = parse_candles(candles_json["candles"])

    if not batch:
        print("⚠️ No complete candles in this batch. Ending fetch.")
        break

    # Remove the first candle of next batch to avoid overlap
    if i > 0:
        batch = batch[1:]

    all_data.extend(batch)

    last_time = candles_json["candles"][-1]["time"]

    if len(all_data) >= TOTAL_CANDLES:
        print(f"✅ Reached {TOTAL_CANDLES} candles.")
        all_data = all_data[:TOTAL_CANDLES]
        break

    time.sleep(1)  # Respect OANDA rate limit

# ==== SAVE RAW DATA TO CSV ====
if all_data:
    df = pd.DataFrame(all_data)
    df.to_csv(OUTPUT_FILE_RAW, index=False)
    print(f"✅ Saved raw data ({len(df)}) candles to {OUTPUT_FILE_RAW}")

    # ==== NORMALIZE OHLC columns ====
    scaler = MinMaxScaler()
    ohlc_cols = ["open", "high", "low", "close"]
    df_norm = df.copy()
    df_norm[ohlc_cols] = scaler.fit_transform(df[ohlc_cols])

    # Save normalized data
    df_norm.to_csv(OUTPUT_FILE_NORM, index=False)
    print(f"✅ Saved normalized data to {OUTPUT_FILE_NORM}")

else:
    print("❌ No data fetched.")
