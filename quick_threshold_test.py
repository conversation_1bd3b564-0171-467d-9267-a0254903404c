import numpy as np
from tensorflow.keras.models import load_model
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

print("🔍 Quick Threshold Optimization")
print("="*50)

# Load test data
print("📊 Loading test data...")
X_test = np.load("X_test.npy")
y_test = np.load("y_test.npy")
print(f"Test samples: {X_test.shape[0]}")

# Load model
print("🧠 Loading model...")
try:
    model = load_model("lstm_candle_predictor.h5")
    print("✅ Model loaded successfully")
except Exception as e:
    print(f"❌ Error loading model: {e}")
    exit()

# Get predictions
print("🔮 Getting predictions...")
y_pred_prob = model.predict(X_test, verbose=0).flatten()
print(f"✅ Got {len(y_pred_prob)} predictions")

# Test different thresholds
thresholds = [0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7]
best_threshold = 0.5
best_balanced_acc = 0

print("\n🎯 Testing Thresholds:")
print("="*80)
print("Threshold | Accuracy | DOWN Recall | UP Recall | Balanced Acc | Recommendation")
print("-"*80)

for threshold in thresholds:
    y_pred = (y_pred_prob > threshold).astype(int)
    accuracy = accuracy_score(y_test, y_pred)
    
    # Calculate recall for each class
    precision, recall, f1, support = precision_recall_fscore_support(y_test, y_pred, average=None, zero_division=0)
    
    down_recall = recall[0] if len(recall) > 0 else 0
    up_recall = recall[1] if len(recall) > 1 else 0
    balanced_acc = (down_recall + up_recall) / 2
    
    # Determine recommendation
    if balanced_acc > best_balanced_acc:
        best_balanced_acc = balanced_acc
        best_threshold = threshold
        recommendation = "⭐ BEST"
    elif abs(down_recall - up_recall) < 0.1:  # Well balanced
        recommendation = "✅ Good"
    elif down_recall > 0.8:
        recommendation = "📉 DOWN-biased"
    elif up_recall > 0.8:
        recommendation = "📈 UP-biased"
    else:
        recommendation = "⚖️ OK"
    
    print(f"{threshold:8.2f} | {accuracy:8.3f} | {down_recall:11.3f} | {up_recall:9.3f} | {balanced_acc:12.3f} | {recommendation}")

print("-"*80)
print(f"🎯 BEST THRESHOLD: {best_threshold:.2f}")
print(f"📊 BEST BALANCED ACCURACY: {best_balanced_acc:.3f}")

# Test the best threshold in detail
print(f"\n📈 Detailed Analysis with Threshold {best_threshold:.2f}:")
print("="*60)

y_pred_best = (y_pred_prob > best_threshold).astype(int)
accuracy = accuracy_score(y_test, y_pred_best)

# Calculate confusion matrix manually
tp = np.sum((y_test == 1) & (y_pred_best == 1))  # True Positives (UP correct)
tn = np.sum((y_test == 0) & (y_pred_best == 0))  # True Negatives (DOWN correct)
fp = np.sum((y_test == 0) & (y_pred_best == 1))  # False Positives (UP wrong)
fn = np.sum((y_test == 1) & (y_pred_best == 0))  # False Negatives (DOWN wrong)

down_precision = tn / (tn + fn) if (tn + fn) > 0 else 0
down_recall = tn / (tn + fp) if (tn + fp) > 0 else 0
up_precision = tp / (tp + fp) if (tp + fp) > 0 else 0
up_recall = tp / (tp + fn) if (tp + fn) > 0 else 0

print(f"Overall Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
print(f"Balanced Accuracy: {best_balanced_acc:.3f} ({best_balanced_acc*100:.1f}%)")
print()
print("Confusion Matrix:")
print(f"                Predicted")
print(f"Actual    DOWN    UP")
print(f"DOWN    {tn:6d}  {fp:6d}")
print(f"UP      {fn:6d}  {tp:6d}")
print()
print("Performance by Class:")
print(f"DOWN - Precision: {down_precision:.3f}, Recall: {down_recall:.3f}")
print(f"UP   - Precision: {up_precision:.3f}, Recall: {up_recall:.3f}")

# Real-time accuracy estimation
total_predictions = len(y_test)
correct_predictions = tp + tn

print(f"\n🎯 REAL-TIME ACCURACY ESTIMATION:")
print("="*50)
print(f"📊 Out of {total_predictions:,} test predictions:")
print(f"✅ Correct: {correct_predictions:,} ({accuracy*100:.1f}%)")
print(f"❌ Wrong: {total_predictions - correct_predictions:,} ({(1-accuracy)*100:.1f}%)")
print()
print(f"📈 UP Movement Detection: {up_recall*100:.1f}% success rate")
print(f"📉 DOWN Movement Detection: {down_recall*100:.1f}% success rate")
print()
print(f"💡 RECOMMENDATION:")
print(f"   Use threshold {best_threshold:.2f} for best balanced predictions")
print(f"   Expected accuracy in real-time: ~{accuracy*100:.1f}%")

# Prediction distribution analysis
up_actual = np.sum(y_test == 1)
down_actual = np.sum(y_test == 0)
up_predicted = np.sum(y_pred_best == 1)
down_predicted = np.sum(y_pred_best == 0)

print(f"\n📊 PREDICTION DISTRIBUTION:")
print("="*40)
print(f"Actual UP movements: {up_actual:,} ({up_actual/total_predictions*100:.1f}%)")
print(f"Actual DOWN movements: {down_actual:,} ({down_actual/total_predictions*100:.1f}%)")
print(f"Predicted UP: {up_predicted:,} ({up_predicted/total_predictions*100:.1f}%)")
print(f"Predicted DOWN: {down_predicted:,} ({down_predicted/total_predictions*100:.1f}%)")

if abs(up_predicted/total_predictions - up_actual/total_predictions) < 0.05:
    print("✅ Well-balanced predictions!")
else:
    print("⚠️ Prediction bias detected")

print(f"\n🚀 Ready for real-time trading with {best_threshold:.2f} threshold!")
