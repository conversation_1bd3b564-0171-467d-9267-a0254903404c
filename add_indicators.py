import pandas as pd
import numpy as np

# Load existing data
print("Loading existing data...")
df = pd.read_csv("historical_data_raw.csv")
print(f"Loaded {len(df)} rows")

def calculate_rsi(prices, period=14):
    """Calculate Relative Strength Index"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD (Moving Average Convergence Divergence)"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    macd_histogram = macd - macd_signal
    return macd, macd_signal, macd_histogram

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, lower_band, sma

def calculate_ema(prices, period=20):
    """Calculate Exponential Moving Average"""
    return prices.ewm(span=period).mean()

def add_technical_indicators(df):
    """Add all technical indicators to the dataframe"""
    df = df.copy()
    
    print("Adding technical indicators...")
    
    # RSI
    df['rsi'] = calculate_rsi(df['close'])
    
    # MACD
    macd, macd_signal, macd_hist = calculate_macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_histogram'] = macd_hist
    
    # Bollinger Bands
    bb_upper, bb_lower, bb_sma = calculate_bollinger_bands(df['close'])
    df['bb_upper'] = bb_upper
    df['bb_lower'] = bb_lower
    df['bb_sma'] = bb_sma
    df['bb_width'] = bb_upper - bb_lower
    df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
    
    # EMAs
    df['ema_20'] = calculate_ema(df['close'], 20)
    df['ema_50'] = calculate_ema(df['close'], 50)
    
    # Price momentum features
    df['price_change'] = df['close'].pct_change()
    df['high_low_ratio'] = df['high'] / df['low']
    df['close_open_ratio'] = df['close'] / df['open']
    
    # Volume features
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    return df

# Add technical indicators
df_with_indicators = add_technical_indicators(df)

# Remove rows with NaN values (from technical indicators)
df_clean = df_with_indicators.dropna()
print(f"After adding indicators and cleaning: {len(df_clean)} candles")

# Save data with technical indicators
df_clean.to_csv("historical_data_normalized.csv", index=False)
print(f"✅ Saved data with technical indicators to historical_data_normalized.csv")

# Display feature summary
feature_cols = [col for col in df_clean.columns if col not in ['time', 'direction']]
print(f"📈 Features available: {len(feature_cols)}")
print(f"Features: {', '.join(feature_cols)}")

print("✅ Technical indicators added successfully!")
