import requests
import pandas as pd
import numpy as np
import datetime
import joblib
from tensorflow.keras.models import load_model
import time

# ==== CONFIG ====
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
ACCOUNT_ID = "101-004-********-001"
INSTRUMENT = "EUR_USD"
GRANULARITY = "M1"
MODEL_PATH = "lstm_candle_predictor.h5"
SCALER_PATH = "feature_scaler.pkl"
SEQUENCE_LENGTH = 60  # Must match training sequence length

# ==== API HEADERS ====
headers = {
    "Authorization": f"Bearer {ACCESS_TOKEN}"
}

def fetch_latest_candles(count=SEQUENCE_LENGTH + 10):
    """Fetch the latest candles for prediction"""
    url = f"https://api-fxpractice.oanda.com/v3/instruments/{INSTRUMENT}/candles"
    params = {
        "granularity": GRANULARITY,
        "count": count,
        "price": "M",
        "smooth": False
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print("❌ Error fetching data:", response.status_code)
        print("Message:", response.text)
        return None
    
    return response.json()

def parse_candles(candles):
    """Parse candles into DataFrame"""
    rows = []
    for candle in candles:
        if candle.get("complete"):
            mid = candle["mid"]
            rows.append({
                "time": candle["time"],
                "open": float(mid["o"]),
                "high": float(mid["h"]),
                "low": float(mid["l"]),
                "close": float(mid["c"]),
                "volume": candle["volume"]
            })
    return pd.DataFrame(rows)

def calculate_rsi(prices, period=14):
    """Calculate RSI"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    macd_histogram = macd - macd_signal
    return macd, macd_signal, macd_histogram

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, lower_band, sma

def calculate_ema(prices, period=20):
    """Calculate EMA"""
    return prices.ewm(span=period).mean()

def add_technical_indicators(df):
    """Add all technical indicators"""
    df = df.copy()
    
    # RSI
    df['rsi'] = calculate_rsi(df['close'])
    
    # MACD
    macd, macd_signal, macd_hist = calculate_macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_histogram'] = macd_hist
    
    # Bollinger Bands
    bb_upper, bb_lower, bb_sma = calculate_bollinger_bands(df['close'])
    df['bb_upper'] = bb_upper
    df['bb_lower'] = bb_lower
    df['bb_sma'] = bb_sma
    df['bb_width'] = bb_upper - bb_lower
    df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
    
    # EMAs
    df['ema_20'] = calculate_ema(df['close'], 20)
    df['ema_50'] = calculate_ema(df['close'], 50)
    
    # Price momentum features
    df['price_change'] = df['close'].pct_change()
    df['high_low_ratio'] = df['high'] / df['low']
    df['close_open_ratio'] = df['close'] / df['open']
    
    # Volume features
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    return df

def get_feature_columns(df):
    """Get feature columns (same as in prepare_dataset.py)"""
    exclude_cols = ['time', 'direction', 'next_close', 'label']
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    return feature_cols

def prepare_prediction_data(df, scaler):
    """Prepare data for prediction"""
    # Add technical indicators
    df_with_indicators = add_technical_indicators(df)
    
    # Remove NaN rows
    df_clean = df_with_indicators.dropna()
    
    if len(df_clean) < SEQUENCE_LENGTH:
        raise ValueError(f"Not enough data for prediction. Need {SEQUENCE_LENGTH}, got {len(df_clean)}")
    
    # Get feature columns
    feature_cols = get_feature_columns(df_clean)
    
    # Get the last sequence
    data = df_clean[feature_cols].values
    last_sequence = data[-SEQUENCE_LENGTH:]
    
    # Normalize using the saved scaler
    last_sequence_normalized = scaler.transform(last_sequence)
    
    # Reshape for model input
    X = last_sequence_normalized.reshape(1, SEQUENCE_LENGTH, -1)
    
    return X, df_clean.iloc[-1]

def predict_next_candle():
    """Make a prediction for the next candle direction"""
    try:
        print("🔄 Fetching latest market data...")
        
        # Fetch latest candles
        candles_data = fetch_latest_candles()
        if not candles_data or not candles_data.get("candles"):
            print("❌ Failed to fetch market data")
            return None
        
        # Parse candles
        df = parse_candles(candles_data["candles"])
        print(f"📊 Fetched {len(df)} candles")
        
        # Load model and scaler
        print("🧠 Loading model and scaler...")
        model = load_model(MODEL_PATH)
        scaler = joblib.load(SCALER_PATH)
        
        # Prepare data
        X, current_candle = prepare_prediction_data(df, scaler)
        
        # Make prediction
        prediction_prob = model.predict(X, verbose=0)[0][0]
        prediction = "UP" if prediction_prob > 0.5 else "DOWN"
        confidence = prediction_prob if prediction_prob > 0.5 else (1 - prediction_prob)
        
        # Display results
        print("\n" + "="*50)
        print("🎯 PREDICTION RESULTS")
        print("="*50)
        print(f"📈 Current Price: {current_candle['close']:.5f}")
        print(f"📊 Current Time: {current_candle['time']}")
        print(f"🔮 Next Candle Prediction: {prediction}")
        print(f"🎯 Confidence: {confidence:.2%}")
        print(f"📊 Raw Probability: {prediction_prob:.4f}")
        print("="*50)
        
        return {
            "prediction": prediction,
            "confidence": confidence,
            "probability": prediction_prob,
            "current_price": current_candle['close'],
            "timestamp": current_candle['time']
        }
        
    except Exception as e:
        print(f"❌ Error making prediction: {e}")
        return None

def continuous_prediction(interval_minutes=1):
    """Run continuous predictions"""
    print(f"🚀 Starting continuous prediction every {interval_minutes} minute(s)")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            result = predict_next_candle()
            if result:
                print(f"⏰ Next prediction in {interval_minutes} minute(s)...")
            else:
                print("⚠️ Prediction failed, retrying...")
            
            time.sleep(interval_minutes * 60)
            
    except KeyboardInterrupt:
        print("\n👋 Stopping continuous prediction...")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "continuous":
        continuous_prediction()
    else:
        print("🎯 Single Prediction Mode")
        predict_next_candle()
        print("\n💡 Tip: Run 'python predict_realtime.py continuous' for continuous predictions")
