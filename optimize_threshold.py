import numpy as np
from tensorflow.keras.models import load_model
from sklearn.metrics import classification_report, accuracy_score
import matplotlib.pyplot as plt

# Load test data and model
print("Loading test data and model...")
X_test = np.load("X_test.npy")
y_test = np.load("y_test.npy")
model = load_model("lstm_candle_predictor.h5")

# Get prediction probabilities
print("Getting prediction probabilities...")
y_pred_prob = model.predict(X_test, verbose=0).flatten()

# Test different thresholds
thresholds = np.arange(0.3, 0.8, 0.05)
results = []

print("\n🔍 Testing different prediction thresholds:")
print("="*60)

for threshold in thresholds:
    y_pred = (y_pred_prob > threshold).astype(int)
    accuracy = accuracy_score(y_test, y_pred)
    
    # Calculate precision and recall for each class
    from sklearn.metrics import precision_recall_fscore_support
    precision, recall, f1, support = precision_recall_fscore_support(y_test, y_pred, average=None)
    
    results.append({
        'threshold': threshold,
        'accuracy': accuracy,
        'down_precision': precision[0],
        'down_recall': recall[0],
        'up_precision': precision[1],
        'up_recall': recall[1],
        'balanced_accuracy': (recall[0] + recall[1]) / 2
    })
    
    print(f"Threshold: {threshold:.2f} | Accuracy: {accuracy:.3f} | "
          f"DOWN R: {recall[0]:.3f} | UP R: {recall[1]:.3f} | "
          f"Balanced: {(recall[0] + recall[1]) / 2:.3f}")

# Find best threshold based on balanced accuracy
best_result = max(results, key=lambda x: x['balanced_accuracy'])
print(f"\n🎯 Best threshold: {best_result['threshold']:.2f}")
print(f"📊 Best balanced accuracy: {best_result['balanced_accuracy']:.3f}")
print(f"📊 Best overall accuracy: {best_result['accuracy']:.3f}")

# Test the best threshold
best_threshold = best_result['threshold']
y_pred_best = (y_pred_prob > best_threshold).astype(int)

print(f"\n📈 Detailed results with threshold {best_threshold:.2f}:")
print("="*60)
print(classification_report(y_test, y_pred_best, target_names=['Down', 'Up']))

# Plot threshold analysis
plt.figure(figsize=(12, 8))

# Plot 1: Accuracy vs Threshold
plt.subplot(2, 2, 1)
thresholds_list = [r['threshold'] for r in results]
accuracies = [r['accuracy'] for r in results]
balanced_accuracies = [r['balanced_accuracy'] for r in results]

plt.plot(thresholds_list, accuracies, 'b-', label='Overall Accuracy', marker='o')
plt.plot(thresholds_list, balanced_accuracies, 'g-', label='Balanced Accuracy', marker='s')
plt.axvline(x=best_threshold, color='r', linestyle='--', label=f'Best Threshold ({best_threshold:.2f})')
plt.xlabel('Threshold')
plt.ylabel('Accuracy')
plt.title('Accuracy vs Prediction Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 2: Recall vs Threshold
plt.subplot(2, 2, 2)
down_recalls = [r['down_recall'] for r in results]
up_recalls = [r['up_recall'] for r in results]

plt.plot(thresholds_list, down_recalls, 'r-', label='DOWN Recall', marker='o')
plt.plot(thresholds_list, up_recalls, 'g-', label='UP Recall', marker='s')
plt.axvline(x=best_threshold, color='b', linestyle='--', label=f'Best Threshold ({best_threshold:.2f})')
plt.xlabel('Threshold')
plt.ylabel('Recall')
plt.title('Recall vs Prediction Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 3: Precision vs Threshold
plt.subplot(2, 2, 3)
down_precisions = [r['down_precision'] for r in results]
up_precisions = [r['up_precision'] for r in results]

plt.plot(thresholds_list, down_precisions, 'r-', label='DOWN Precision', marker='o')
plt.plot(thresholds_list, up_precisions, 'g-', label='UP Precision', marker='s')
plt.axvline(x=best_threshold, color='b', linestyle='--', label=f'Best Threshold ({best_threshold:.2f})')
plt.xlabel('Threshold')
plt.ylabel('Precision')
plt.title('Precision vs Prediction Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 4: Prediction Distribution
plt.subplot(2, 2, 4)
plt.hist(y_pred_prob[y_test == 0], bins=30, alpha=0.7, label='Actual DOWN', color='red')
plt.hist(y_pred_prob[y_test == 1], bins=30, alpha=0.7, label='Actual UP', color='green')
plt.axvline(x=0.5, color='blue', linestyle='-', label='Default Threshold (0.5)')
plt.axvline(x=best_threshold, color='orange', linestyle='--', label=f'Best Threshold ({best_threshold:.2f})')
plt.xlabel('Prediction Probability')
plt.ylabel('Frequency')
plt.title('Prediction Probability Distribution')
plt.legend()

plt.tight_layout()
plt.savefig('threshold_optimization.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\n📊 Threshold optimization chart saved as 'threshold_optimization.png'")
print(f"💡 Recommendation: Use threshold {best_threshold:.2f} for better balanced predictions")
