import requests
import pandas as pd
import numpy as np
import datetime
import joblib
from tensorflow.keras.models import load_model
import time
import sys

# ==== CONFIG ====
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
ACCOUNT_ID = "101-004-********-001"
INSTRUMENT = "EUR_USD"
GRANULARITY = "M1"
MODEL_PATH = "lstm_candle_predictor.h5"
SCALER_PATH = "feature_scaler.pkl"
SEQUENCE_LENGTH = 60
FETCH_COUNT = 110  # 60 for sequence + 50 for technical indicators
OPTIMIZED_THRESHOLD = 0.45

# ==== API HEADERS ====
headers = {
    "Authorization": f"Bearer {ACCESS_TOKEN}"
}

def fetch_latest_candles():
    """Fetch the latest candles for prediction"""
    url = f"https://api-fxpractice.oanda.com/v3/instruments/{INSTRUMENT}/candles"
    params = {
        "granularity": GRANULARITY,
        "count": FETCH_COUNT,
        "price": "M",
        "smooth": False
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print(f"❌ API Error: {response.status_code}")
        return None
    
    return response.json()

def parse_candles(candles):
    """Parse candles into DataFrame"""
    rows = []
    for candle in candles:
        if candle.get("complete"):
            mid = candle["mid"]
            rows.append({
                "time": candle["time"],
                "open": float(mid["o"]),
                "high": float(mid["h"]),
                "low": float(mid["l"]),
                "close": float(mid["c"]),
                "volume": candle["volume"]
            })
    return pd.DataFrame(rows)

def calculate_technical_indicators(df):
    """Add all technical indicators"""
    df = df.copy()
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # MACD
    ema_fast = df['close'].ewm(span=12).mean()
    ema_slow = df['close'].ewm(span=26).mean()
    df['macd'] = ema_fast - ema_slow
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # Bollinger Bands
    bb_sma = df['close'].rolling(window=20).mean()
    bb_std = df['close'].rolling(window=20).std()
    df['bb_upper'] = bb_sma + (bb_std * 2)
    df['bb_lower'] = bb_sma - (bb_std * 2)
    df['bb_sma'] = bb_sma
    df['bb_width'] = df['bb_upper'] - df['bb_lower']
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # EMAs
    df['ema_20'] = df['close'].ewm(span=20).mean()
    df['ema_50'] = df['close'].ewm(span=50).mean()
    
    # Price momentum features
    df['price_change'] = df['close'].pct_change()
    df['high_low_ratio'] = df['high'] / df['low']
    df['close_open_ratio'] = df['close'] / df['open']
    
    # Volume features
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    return df

def get_feature_columns(df):
    """Get feature columns"""
    exclude_cols = ['time', 'direction', 'next_close', 'label']
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    return feature_cols

def generate_live_signal():
    """Generate a single live trading signal"""
    try:
        # Fetch data
        print(f"📡 Fetching {FETCH_COUNT} latest candles from OANDA...")
        candles_data = fetch_latest_candles()
        if not candles_data or not candles_data.get("candles"):
            print("❌ Failed to fetch market data")
            return None
        
        # Parse candles
        df = parse_candles(candles_data["candles"])
        print(f"✅ Received {len(df)} completed candles")
        
        # Add technical indicators
        print("🔧 Calculating 21 technical indicators...")
        df_with_indicators = calculate_technical_indicators(df)
        
        # Remove NaN rows
        df_clean = df_with_indicators.dropna()
        print(f"📊 After technical analysis: {len(df_clean)} usable candles")
        
        if len(df_clean) < SEQUENCE_LENGTH:
            print(f"❌ Not enough data: need {SEQUENCE_LENGTH}, got {len(df_clean)}")
            return None
        
        # Prepare features
        feature_cols = get_feature_columns(df_clean)
        print(f"🎯 Using {len(feature_cols)} features per candle")
        
        # Get the last sequence for prediction
        data = df_clean[feature_cols].values
        last_sequence = data[-SEQUENCE_LENGTH:]
        print(f"🔍 Using last {SEQUENCE_LENGTH} candles for prediction")
        
        # Load and apply scaler
        scaler = joblib.load(SCALER_PATH)
        last_sequence_normalized = scaler.transform(last_sequence)
        
        # Reshape for model input
        X = last_sequence_normalized.reshape(1, SEQUENCE_LENGTH, -1)
        print(f"📐 Input shape: {X.shape}")
        
        # Load model and predict
        model = load_model(MODEL_PATH)
        prediction_prob = model.predict(X, verbose=0)[0][0]
        
        # Apply threshold
        prediction = "UP" if prediction_prob > OPTIMIZED_THRESHOLD else "DOWN"
        confidence = prediction_prob if prediction_prob > OPTIMIZED_THRESHOLD else (1 - prediction_prob)
        
        # Get current candle info
        current_candle = df_clean.iloc[-1]
        
        # Determine signal strength
        if confidence > 0.7:
            strength = "🔥 STRONG"
            action = "EXECUTE TRADE"
        elif confidence > 0.6:
            strength = "⚡ MEDIUM"
            action = "CONSIDER TRADE"
        else:
            strength = "⚠️ WEAK"
            action = "WAIT"
        
        return {
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "market_time": current_candle['time'],
            "current_price": current_candle['close'],
            "prediction": prediction,
            "confidence": confidence,
            "probability": prediction_prob,
            "strength": strength,
            "action": action,
            "candles_fetched": len(df),
            "candles_used": len(df_clean),
            "sequence_length": SEQUENCE_LENGTH,
            "features_count": len(feature_cols)
        }
        
    except Exception as e:
        print(f"❌ Error generating signal: {e}")
        return None

def display_signal(signal, signal_number):
    """Display a formatted trading signal"""
    print("\n" + "="*70)
    print(f"🚨 LIVE TRADING SIGNAL #{signal_number}")
    print("="*70)
    print(f"⏰ Signal Time: {signal['timestamp']}")
    print(f"📊 Market Time: {signal['market_time']}")
    print(f"📈 {INSTRUMENT} Price: {signal['current_price']:.5f}")
    print("-"*70)
    print(f"🎯 PREDICTION: {signal['prediction']}")
    print(f"💪 Strength: {signal['strength']}")
    print(f"🎲 Confidence: {signal['confidence']:.1%}")
    print(f"📊 Raw Probability: {signal['probability']:.4f}")
    print(f"⚙️ Threshold: {OPTIMIZED_THRESHOLD}")
    print("-"*70)
    print(f"💡 RECOMMENDATION: {signal['action']}")
    print("-"*70)
    print(f"📡 Data Details:")
    print(f"   • Candles Fetched: {signal['candles_fetched']}")
    print(f"   • Candles Used: {signal['candles_used']}")
    print(f"   • Sequence Length: {signal['sequence_length']}")
    print(f"   • Features per Candle: {signal['features_count']}")
    print("="*70)

def run_live_bot(interval_seconds=60, max_signals=None):
    """Run the live trading bot"""
    print("🚀 STARTING LIVE TRADING SIGNAL BOT")
    print("="*70)
    print(f"📊 Instrument: {INSTRUMENT}")
    print(f"⏱️ Signal Interval: {interval_seconds} seconds")
    print(f"🎯 Threshold: {OPTIMIZED_THRESHOLD}")
    print(f"📡 Data per Signal: {FETCH_COUNT} candles")
    print(f"🔍 Sequence Length: {SEQUENCE_LENGTH} candles")
    print(f"🎲 Expected Accuracy: ~53% overall, 84% for UP signals")
    print("="*70)
    print("Press Ctrl+C to stop the bot")
    print("="*70)
    
    signal_count = 0
    
    try:
        while True:
            signal_count += 1
            
            print(f"\n🔄 Generating Signal #{signal_count}...")
            signal = generate_live_signal()
            
            if signal:
                display_signal(signal, signal_count)
                
                # Check if we should stop
                if max_signals and signal_count >= max_signals:
                    print(f"\n✅ Completed {max_signals} signals as requested")
                    break
                    
                if not max_signals:  # Only wait if running continuously
                    print(f"\n⏳ Next signal in {interval_seconds} seconds...")
                    time.sleep(interval_seconds)
            else:
                print("❌ Signal generation failed, retrying in 30 seconds...")
                time.sleep(30)
                
    except KeyboardInterrupt:
        print(f"\n\n👋 Bot stopped by user after {signal_count} signals")
    except Exception as e:
        print(f"\n❌ Bot error: {e}")
    
    print("\n🎯 LIVE TRADING BOT SESSION COMPLETE")

if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            # Demo mode: 3 signals, no waiting
            print("🎯 DEMO MODE: Generating 3 quick signals")
            run_live_bot(interval_seconds=10, max_signals=3)
        elif sys.argv[1] == "single":
            # Single signal mode
            print("🎯 SINGLE SIGNAL MODE")
            signal = generate_live_signal()
            if signal:
                display_signal(signal, 1)
        else:
            try:
                interval = int(sys.argv[1])
                run_live_bot(interval_seconds=interval)
            except ValueError:
                print("Usage: python live_trading_bot.py [interval_seconds|demo|single]")
    else:
        # Default: 60-second intervals
        run_live_bot(interval_seconds=60)
