import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split

# ==== CONFIG ====
DATA_FILE = "historical_data_normalized.csv"
SEQUENCE_LENGTH = 30  # Number of candles as input
TEST_SIZE = 0.1       # 10% test data split
RANDOM_STATE = 42

def create_labels(df):
    # Label 1 if next candle close > current candle close else 0
    df['next_close'] = df['close'].shift(-1)
    df['label'] = (df['next_close'] > df['close']).astype(int)
    df = df[:-1]  # Remove last row with no next_close
    return df

def create_sequences(df, seq_len):
    X = []
    y = []

    data = df[['open', 'high', 'low', 'close', 'volume']].values
    labels = df['label'].values

    for i in range(len(df) - seq_len):
        X.append(data[i:i+seq_len])
        y.append(labels[i+seq_len])
    
    return np.array(X), np.array(y)

def main():
    print("Loading data...")
    df = pd.read_csv(DATA_FILE)

    print("Creating labels...")
    df = create_labels(df)

    print("Creating sequences...")
    X, y = create_sequences(df, SEQUENCE_LENGTH)

    print(f"Total sequences: {X.shape[0]}")

    print("Splitting data into train/test sets...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=TEST_SIZE, random_state=RANDOM_STATE, shuffle=True
    )

    print(f"Train size: {X_train.shape[0]}, Test size: {X_test.shape[0]}")

    # Save to npy files for later training
    np.save("X_train.npy", X_train)
    np.save("X_test.npy", X_test)
    np.save("y_train.npy", y_train)
    np.save("y_test.npy", y_test)

    print("Saved preprocessed datasets: X_train.npy, X_test.npy, y_train.npy, y_test.npy")

if __name__ == "__main__":
    main()
