import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from imblearn.over_sampling import SMOTE
import joblib

# ==== CONFIG ====
DATA_FILE = "historical_data_normalized.csv"
SEQUENCE_LENGTH = 60  # Increased from 30 for better patterns
TEST_SIZE = 0.1       # 10% test data split
RANDOM_STATE = 42
SCALER_SAVE_PATH = "feature_scaler.pkl"
USE_SMOTE = False     # Handle class imbalance (disabled for speed)

def get_feature_columns(df):
    """Get all feature columns (excluding time, direction, and label columns)"""
    exclude_cols = ['time', 'direction', 'next_close', 'label']
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    return feature_cols

def create_labels(df):
    """Create labels for next candle direction prediction"""
    # Label 1 if next candle close > current candle close else 0
    df['next_close'] = df['close'].shift(-1)
    df['label'] = (df['next_close'] > df['close']).astype(int)
    df = df[:-1]  # Remove last row with no next_close
    return df

def normalize_features(X_train, X_test, feature_cols):
    """Normalize features using MinMaxScaler fitted only on training data"""
    print("🔧 Normalizing features...")

    # Reshape for scaling (combine all sequences)
    X_train_reshaped = X_train.reshape(-1, X_train.shape[-1])
    X_test_reshaped = X_test.reshape(-1, X_test.shape[-1])

    # Fit scaler only on training data
    scaler = MinMaxScaler()
    X_train_scaled = scaler.fit_transform(X_train_reshaped)
    X_test_scaled = scaler.transform(X_test_reshaped)

    # Reshape back to sequences
    X_train_normalized = X_train_scaled.reshape(X_train.shape)
    X_test_normalized = X_test_scaled.reshape(X_test.shape)

    # Save scaler for later use in predictions
    joblib.dump(scaler, SCALER_SAVE_PATH)
    print(f"💾 Saved feature scaler to {SCALER_SAVE_PATH}")

    return X_train_normalized, X_test_normalized, scaler

def handle_class_imbalance(X, y):
    """Handle class imbalance using SMOTE"""
    if not USE_SMOTE:
        return X, y

    print("⚖️ Handling class imbalance with SMOTE...")

    # Check class distribution
    unique, counts = np.unique(y, return_counts=True)
    print(f"Original class distribution: {dict(zip(unique, counts))}")

    # Reshape X for SMOTE (flatten sequences)
    X_flattened = X.reshape(X.shape[0], -1)

    # Apply SMOTE
    smote = SMOTE(random_state=RANDOM_STATE)
    X_balanced, y_balanced = smote.fit_resample(X_flattened, y)

    # Reshape back to sequences
    X_balanced = X_balanced.reshape(-1, X.shape[1], X.shape[2])

    # Check new class distribution
    unique, counts = np.unique(y_balanced, return_counts=True)
    print(f"Balanced class distribution: {dict(zip(unique, counts))}")

    return X_balanced, y_balanced

def create_sequences(df, seq_len, feature_cols):
    """Create sequences using all available features"""
    X = []
    y = []

    # Use all feature columns instead of just OHLCV
    data = df[feature_cols].values
    labels = df['label'].values

    for i in range(len(df) - seq_len):
        X.append(data[i:i+seq_len])
        y.append(labels[i+seq_len])

    return np.array(X), np.array(y)

def main():
    print("Loading data...")
    df = pd.read_csv(DATA_FILE)
    print(f"Loaded {len(df)} rows")

    print("Getting feature columns...")
    feature_cols = get_feature_columns(df)
    print(f"📊 Using {len(feature_cols)} features: {feature_cols}")

    print("Creating labels...")
    df = create_labels(df)

    print("Creating sequences...")
    X, y = create_sequences(df, SEQUENCE_LENGTH, feature_cols)
    print(f"Total sequences: {X.shape[0]}, Features per timestep: {X.shape[2]}")

    print("Splitting data into train/test sets...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=TEST_SIZE, random_state=RANDOM_STATE, shuffle=True
    )
    print(f"Train size: {X_train.shape[0]}, Test size: {X_test.shape[0]}")

    # Normalize features (fit only on training data)
    X_train_norm, X_test_norm, scaler = normalize_features(X_train, X_test, feature_cols)

    # Handle class imbalance
    X_train_balanced, y_train_balanced = handle_class_imbalance(X_train_norm, y_train)

    print(f"Final training set: {X_train_balanced.shape[0]} samples")
    print(f"Final test set: {X_test_norm.shape[0]} samples")

    # Save to npy files for later training
    np.save("X_train.npy", X_train_balanced)
    np.save("X_test.npy", X_test_norm)
    np.save("y_train.npy", y_train_balanced)
    np.save("y_test.npy", y_test)

    print("✅ Saved preprocessed datasets: X_train.npy, X_test.npy, y_train.npy, y_test.npy")
    print(f"✅ Saved feature scaler: {SCALER_SAVE_PATH}")

    # Print final statistics
    print("\n📈 Final Dataset Statistics:")
    print(f"Training samples: {X_train_balanced.shape[0]}")
    print(f"Test samples: {X_test_norm.shape[0]}")
    print(f"Sequence length: {X_train_balanced.shape[1]}")
    print(f"Features per timestep: {X_train_balanced.shape[2]}")

    unique, counts = np.unique(y_train_balanced, return_counts=True)
    print(f"Training class distribution: {dict(zip(unique, counts))}")
    unique, counts = np.unique(y_test, return_counts=True)
    print(f"Test class distribution: {dict(zip(unique, counts))}")

if __name__ == "__main__":
    main()
