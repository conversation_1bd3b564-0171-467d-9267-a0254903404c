import numpy as np
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint

# ==== CONFIG ====
X_train_file = "X_train.npy"
X_test_file = "X_test.npy"
y_train_file = "y_train.npy"
y_test_file = "y_test.npy"
MODEL_SAVE_PATH = "lstm_candle_predictor.h5"
EPOCHS = 20
BATCH_SIZE = 64

def build_model(input_shape):
    model = Sequential()
    model.add(LSTM(64, return_sequences=True, input_shape=input_shape))
    model.add(Dropout(0.2))
    model.add(LSTM(32))
    model.add(Dropout(0.2))
    model.add(Dense(1, activation='sigmoid'))  # Binary classification

    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model

def main():
    print("Loading data...")
    X_train = np.load(X_train_file)
    X_test = np.load(X_test_file)
    y_train = np.load(y_train_file)
    y_test = np.load(y_test_file)

    print(f"Training samples: {X_train.shape[0]}, Test samples: {X_test.shape[0]}")
    print(f"Input shape: {X_train.shape[1:]}")

    model = build_model(X_train.shape[1:])

    # Callbacks
    early_stop = EarlyStopping(monitor='val_loss', patience=3, restore_best_weights=True)
    checkpoint = ModelCheckpoint(MODEL_SAVE_PATH, save_best_only=True, monitor='val_loss')

    print("Starting training...")
    history = model.fit(
        X_train, y_train,
        epochs=EPOCHS,
        batch_size=BATCH_SIZE,
        validation_split=0.1,
        callbacks=[early_stop, checkpoint]
    )

    print(f"Model saved to {MODEL_SAVE_PATH}")

if __name__ == "__main__":
    main()
