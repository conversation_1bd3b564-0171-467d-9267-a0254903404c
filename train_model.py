import numpy as np
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D, Flatten, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from tensorflow.keras.optimizers import AdamW
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt

# ==== CONFIG ====
X_train_file = "X_train.npy"
X_test_file = "X_test.npy"
y_train_file = "y_train.npy"
y_test_file = "y_test.npy"
MODEL_SAVE_PATH = "lstm_candle_predictor.h5"
EPOCHS = 50
BATCH_SIZE = 32
LEARNING_RATE = 0.0005
MODEL_TYPE = "cnn_lstm"  # Options: "stacked_lstm", "cnn_lstm", "simple_lstm"

def build_stacked_lstm(input_shape):
    """Enhanced stacked LSTM with batch normalization"""
    model = Sequential()

    # First LSTM layer
    model.add(LSTM(128, return_sequences=True, input_shape=input_shape))
    model.add(BatchNormalization())
    model.add(Dropout(0.3))

    # Second LSTM layer
    model.add(LSTM(64, return_sequences=True))
    model.add(BatchNormalization())
    model.add(Dropout(0.3))

    # Third LSTM layer
    model.add(LSTM(32))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))

    # Dense layers
    model.add(Dense(16, activation='relu'))
    model.add(Dropout(0.2))
    model.add(Dense(1, activation='sigmoid'))

    return model

def build_cnn_lstm(input_shape):
    """CNN + LSTM hybrid architecture"""
    model = Sequential()

    # CNN layers for feature extraction
    model.add(Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=input_shape))
    model.add(BatchNormalization())
    model.add(MaxPooling1D(pool_size=2))
    model.add(Dropout(0.2))

    model.add(Conv1D(filters=32, kernel_size=3, activation='relu'))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))

    # LSTM layers
    model.add(LSTM(64, return_sequences=True))
    model.add(BatchNormalization())
    model.add(Dropout(0.3))

    model.add(LSTM(32))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))

    # Dense layers
    model.add(Dense(16, activation='relu'))
    model.add(Dropout(0.2))
    model.add(Dense(1, activation='sigmoid'))

    return model

def build_simple_lstm(input_shape):
    """Simple LSTM for comparison"""
    model = Sequential()
    model.add(LSTM(64, return_sequences=True, input_shape=input_shape))
    model.add(Dropout(0.2))
    model.add(LSTM(32))
    model.add(Dropout(0.2))
    model.add(Dense(1, activation='sigmoid'))
    return model

def build_model(input_shape, model_type=MODEL_TYPE):
    """Build model based on specified type"""
    print(f"🏗️ Building {model_type} model...")

    if model_type == "stacked_lstm":
        model = build_stacked_lstm(input_shape)
    elif model_type == "cnn_lstm":
        model = build_cnn_lstm(input_shape)
    elif model_type == "simple_lstm":
        model = build_simple_lstm(input_shape)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

    # Compile with AdamW optimizer and class weights
    optimizer = AdamW(learning_rate=LEARNING_RATE, weight_decay=0.01)
    model.compile(
        optimizer=optimizer,
        loss='binary_crossentropy',
        metrics=['accuracy']
    )

    print(f"📊 Model parameters: {model.count_params():,}")
    return model

def evaluate_model(model, X_test, y_test):
    """Evaluate model performance with detailed metrics"""
    print("\n📊 Evaluating model performance...")

    # Make predictions
    y_pred_prob = model.predict(X_test, verbose=0)
    y_pred = (y_pred_prob > 0.5).astype(int).flatten()

    # Calculate metrics
    test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)

    print(f"Test Loss: {test_loss:.4f}")
    print(f"Test Accuracy: {test_accuracy:.4f}")

    # Classification report
    print("\n📈 Classification Report:")
    print(classification_report(y_test, y_pred, target_names=['Down', 'Up']))

    # Confusion matrix
    print("\n🔍 Confusion Matrix:")
    cm = confusion_matrix(y_test, y_pred)
    print(cm)

    return test_accuracy, y_pred_prob

def plot_training_history(history):
    """Plot training history"""
    try:
        plt.figure(figsize=(12, 4))

        # Plot accuracy
        plt.subplot(1, 2, 1)
        plt.plot(history.history['accuracy'], label='Training Accuracy')
        plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
        plt.title('Model Accuracy')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()

        # Plot loss
        plt.subplot(1, 2, 2)
        plt.plot(history.history['loss'], label='Training Loss')
        plt.plot(history.history['val_loss'], label='Validation Loss')
        plt.title('Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("📊 Training history saved as 'training_history.png'")
    except Exception as e:
        print(f"⚠️ Could not plot training history: {e}")

def calculate_class_weights(y_train):
    """Calculate moderate class weights to handle imbalance"""
    from sklearn.utils.class_weight import compute_class_weight

    classes = np.unique(y_train)
    class_weights = compute_class_weight('balanced', classes=classes, y=y_train)

    # Moderate the weights to avoid extreme bias
    class_weights = 1.0 + 0.3 * (class_weights - 1.0)  # Reduce weight difference
    class_weight_dict = dict(zip(classes, class_weights))

    print(f"📊 Moderate class weights: {class_weight_dict}")
    return class_weight_dict

def main():
    print("Loading data...")
    X_train = np.load(X_train_file)
    X_test = np.load(X_test_file)
    y_train = np.load(y_train_file)
    y_test = np.load(y_test_file)

    print(f"Training samples: {X_train.shape[0]}, Test samples: {X_test.shape[0]}")
    print(f"Input shape: {X_train.shape[1:]}")
    print(f"Features per timestep: {X_train.shape[2]}")

    # Check class distribution
    unique, counts = np.unique(y_train, return_counts=True)
    print(f"Training class distribution: {dict(zip(unique, counts))}")

    # Calculate class weights
    class_weights = calculate_class_weights(y_train)

    model = build_model(X_train.shape[1:])

    # Print model summary
    print("\n🏗️ Model Architecture:")
    model.summary()

    # Enhanced callbacks
    early_stop = EarlyStopping(
        monitor='val_accuracy',  # Changed from val_loss to val_accuracy
        patience=7,
        restore_best_weights=True,
        verbose=1
    )

    checkpoint = ModelCheckpoint(
        MODEL_SAVE_PATH,
        save_best_only=True,
        monitor='val_accuracy',  # Changed from val_loss to val_accuracy
        mode='max',
        verbose=1
    )

    reduce_lr = ReduceLROnPlateau(
        monitor='val_accuracy',
        factor=0.5,
        patience=3,
        min_lr=1e-7,
        verbose=1
    )

    print("🚀 Starting training...")
    history = model.fit(
        X_train, y_train,
        epochs=EPOCHS,
        batch_size=BATCH_SIZE,
        validation_split=0.15,  # Increased validation split
        callbacks=[early_stop, checkpoint, reduce_lr],
        class_weight=class_weights,  # Add class weights to handle imbalance
        verbose=1
    )

    print(f"✅ Model saved to {MODEL_SAVE_PATH}")

    # Plot training history
    plot_training_history(history)

    # Evaluate model
    test_accuracy, predictions = evaluate_model(model, X_test, y_test)

    print(f"\n🎯 Final Test Accuracy: {test_accuracy:.4f}")
    print("🎉 Training completed successfully!")

if __name__ == "__main__":
    main()
